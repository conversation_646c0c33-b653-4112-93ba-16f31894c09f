# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * documents_account
#
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-06 18:49+0000\n"
"PO-Revision-Date: 2025-08-29 15:40+0000\n"
"Last-Translator: Oakarmin Iron <<EMAIL>>\n"
"Language-Team: Burmese <https://translate.odoo.com/projects/odoo-18/"
"documents_account/my/>\n"
"Language: my\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.12.2\n"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "<b>Select</b> this page to continue."
msgstr ""

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.account_move_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr ""

#. module: documents_account
#: model:ir.model.constraint,message:documents_account.constraint_documents_account_folder_setting_journal_unique
msgid "A setting already exists for this journal"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__documents_account_settings
msgid "Accounting "
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_report
msgid "Accounting Report"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__company_account_folder_id
#: model:ir.model.fields,field_description:documents_account.field_res_company__account_folder_id
msgid "Accounting Workspace"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Action"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "All"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_ir_attachment
msgid "Attachment"
msgstr ""

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Centralize accounting files and documents"
msgstr ""

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "Check them"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click on a card to <b>select the document</b>."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click the cross to <b>exit preview</b>."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Close (Esc)"
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_company
msgid "Companies"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__company_id
msgid "Company"
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/account_report.py:0
msgid "Copy to Documents"
msgstr ""

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_credit_note
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_credit_note_code
msgid "Create Customer Credit Note"
msgstr ""

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_customer_invoice
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_customer_invoice_code
msgid "Create Customer Invoice"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_misc_entry
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_misc_entry_code
msgid "Create Misc Entry"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_bill
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_bill_code
msgid "Create Vendor Bill"
msgstr ""

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_receipt
msgid "Create Vendor Receipt"
msgstr ""

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_refund
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_refund_code
msgid "Create Vendor Refund"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Create a new document for the remaining page, it will be handled later."
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_uid
msgid "Created by"
msgstr "ဖန်တီးသူ"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_date
msgid "Created on"
msgstr "ဖန်တီးချိန်"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__display_name
msgid "Display Name"
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_document
msgid "Document"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__documents_account_settings
msgid "Documents Account Settings"
msgstr ""

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/account_report.py:0
msgid "Export"
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Finance"
msgstr ""

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_fs
msgid "Financial Statement"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,help:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder where to save the generated file"
msgstr ""

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/account_journal.py:0
msgid "Generated Bank Statements"
msgstr ""

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/wizard/account_reports_export_wizard.py:0
msgid "Generated Documents"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_bank_statement_line__has_documents
#: model:ir.model.fields,field_description:documents_account.field_account_move__has_documents
msgid "Has Documents"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_document__has_embedded_pdf
msgid "Has Embedded PDF"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__id
msgid "ID"
msgstr ""

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_bank_statement
#: model:ir.actions.server,name:documents_account.ir_actions_server_bank_statement_code
msgid "Import Bank Statement"
msgstr ""

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/documents_document.py:0
msgid "Invoices"
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_journal
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__journal_id
msgid "Journal"
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_account_folder_setting
msgid "Journal and Folder settings"
msgstr ""

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Journals"
msgstr ""

#. module: documents_account
#: model:ir.actions.act_window,name:documents_account.action_folder_settings_installer
msgid "Journals to synchronize"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_uid
msgid "Last Updated by"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_date
msgid "Last Updated on"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Let's process documents in this folder.<br/> <i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Let's process these bills: turn them into vendor bills."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Let's process this document now."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Let's tag this mail as Finance<br/> <i>Tips: actions can be tailored to your"
" process, according to the folder.</i>"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Open the actions menu"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_bank_statement_line__suspense_statement_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_move__suspense_statement_line_id
msgid "Request document from a bank statement line"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Send To Finance"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Split PDF"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__tag_ids
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__tag_ids
msgid "Tags"
msgstr ""

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_vat
msgid "Tax Statement"
msgstr ""

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "This invoice has been initiated by a bank transaction."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "This should be processed as a misc entry."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__folder_id
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Workspace"
msgstr ""

#. module: documents_account
#: model_terms:web_tour.tour,rainbow_man_message:documents_account.documents_account_tour
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br>The tour is "
"complete. Try uploading your own documents now."
msgstr ""

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/documents_document.py:0
msgid "You can not create account move on folder."
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__account_folder_id
msgid "account default folder"
msgstr ""

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "to mark this invoice as paid."
msgstr ""
