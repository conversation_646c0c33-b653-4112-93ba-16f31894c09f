# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * documents_fleet
#
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 09:15+0000\n"
"PO-Revision-Date: 2025-08-28 06:24+0000\n"
"Last-Translator: Oakarmin Iron <<EMAIL>>\n"
"Language-Team: Burmese <https://translate.odoo.com/projects/odoo-18/"
"documents_fleet/my/>\n"
"Language: my\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.12.2\n"

#. module: documents_fleet
#: model:ir.model.fields,field_description:documents_fleet.field_fleet_vehicle__documents_fleet_settings
msgid "Centralize Fleet Documents"
msgstr ""

#. module: documents_fleet
#: model_terms:ir.ui.view,arch_db:documents_fleet.res_config_settings_view_form_inherit_documents_fleet
msgid "Centralize your Fleet' documents (fines, etc.)"
msgstr ""

#. module: documents_fleet
#: model:ir.model,name:documents_fleet.model_res_company
msgid "Companies"
msgstr ""

#. module: documents_fleet
#: model:ir.model,name:documents_fleet.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: documents_fleet
#: model_terms:ir.ui.view,arch_db:documents_fleet.res_config_settings_view_form_inherit_documents_fleet
msgid "Default Tags"
msgstr ""

#. module: documents_fleet
#: model:ir.model.fields,field_description:documents_fleet.field_fleet_vehicle__document_count
#: model_terms:ir.ui.view,arch_db:documents_fleet.fleet_vehicle_view_form_inherit_documents_fleet
msgid "Documents"
msgstr ""

#. module: documents_fleet
#: model:ir.model.fields,field_description:documents_fleet.field_res_company__documents_fleet_settings
msgid "Documents Fleet Settings"
msgstr ""

#. module: documents_fleet
#: model:ir.model.fields,field_description:documents_fleet.field_res_company__documents_fleet_tags
msgid "Documents Fleet Tags"
msgstr ""

#. module: documents_fleet
#: model:ir.model.fields,field_description:documents_fleet.field_res_config_settings__documents_fleet_settings
msgid "Fleet"
msgstr ""

#. module: documents_fleet
#: model:documents.tag,name:documents_fleet.documents_fine
msgid "Fleet > Fine"
msgstr ""

#. module: documents_fleet
#: model:documents.tag,name:documents_fleet.documents_vehicles
msgid "Fleet > Vehicles Documents"
msgstr ""

#. module: documents_fleet
#: model:ir.model.fields,field_description:documents_fleet.field_res_config_settings__documents_fleet_tags
msgid "Fleet Default Tags"
msgstr ""

#. module: documents_fleet
#: model:ir.model.fields,field_description:documents_fleet.field_res_company__documents_fleet_folder
#: model:ir.model.fields,field_description:documents_fleet.field_res_config_settings__documents_fleet_folder
msgid "Fleet Workspace"
msgstr ""

#. module: documents_fleet
#: model:ir.actions.server,name:documents_fleet.ir_actions_server_link_to_vehicule
msgid "Link to a vehicle"
msgstr ""

#. module: documents_fleet
#: model:ir.model,name:documents_fleet.model_fleet_vehicle
msgid "Vehicle"
msgstr "ယာဉ်"

#. module: documents_fleet
#: model_terms:ir.ui.view,arch_db:documents_fleet.res_config_settings_view_form_inherit_documents_fleet
msgid "Workspace"
msgstr ""
