# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * documents_hr_contract
#
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON>@gmail.com>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 09:15+0000\n"
"PO-Revision-Date: 2025-08-25 08:08+0000\n"
"Last-Translator: <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>\n"
"Language-Team: Serbian (Latin script) <https://translate.odoo.com/projects/"
"odoo-18/documents_hr_contract/sr_Latn/>\n"
"Language: sr@latin\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 5.12.2\n"

#. module: documents_hr_contract
#: model_terms:ir.ui.view,arch_db:documents_hr_contract.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Default tags</span>"
msgstr "<span class=\"o_form_label\">Podrazumevane oznake</span>"

#. module: documents_hr_contract
#: model:ir.model,name:documents_hr_contract.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: documents_hr_contract
#: model:ir.model,name:documents_hr_contract.model_res_config_settings
msgid "Config Settings"
msgstr "Podešavanja konfiguracije"

#. module: documents_hr_contract
#: model:ir.model.fields,field_description:documents_hr_contract.field_res_config_settings__documents_hr_contracts_tags
msgid "Contracts"
msgstr "Ugovori"

#. module: documents_hr_contract
#: model:documents.tag,name:documents_hr_contract.documents_hr_tag_contracts
msgid "Contracts (HR)"
msgstr "Ugovori (HR)"

#. module: documents_hr_contract
#: model:ir.model.fields,field_description:documents_hr_contract.field_res_company__documents_hr_contracts_tags
msgid "Documents Hr Contracts Tags"
msgstr "Dokumenta oznake Hr Ugovora"

#. module: documents_hr_contract
#: model:ir.model,name:documents_hr_contract.model_hr_contract
msgid "Employee Contract"
msgstr "Ugovor o zaposlenju"

#. module: documents_hr_contract
#: model:documents.tag,name:documents_hr_contract.document_tag_signature_request
msgid "Signature Request"
msgstr "Zahtev za potpis"

#. module: documents_hr_contract
#: model:ir.model,name:documents_hr_contract.model_documents_tag
msgid "Tag"
msgstr "Oznaka"

#. module: documents_hr_contract
#. odoo-python
#: code:addons/documents_hr_contract/models/documents_tag.py:0
msgid ""
"You cannot delete this tag as it is used to link employee contracts and "
"signatures."
msgstr ""
"Ne možete obrisati ovu oznaku jer se koristi za povezivanje ugovora i "
"potpisa zaposlenih."
